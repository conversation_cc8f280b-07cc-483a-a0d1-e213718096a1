//
//  MyWidgetLargeView.swift
//  MyWidgetExtensionExtension
//
//  Created by lifubing on 2025/9/21.
//

import WidgetKit
import SwiftUI
import AppIntents

struct MyWidgetLargeView: View {
    var entry: PhotoEntry
    
    var body: some View {
        
        GeometryReader { geometry in
            
            let sizeMax = 1.3
            let offsetSize = 0.14
            
            ZStack {
                // 背景图片或渐变，完全填满Widget
                if let photo = entry.photo, let photoIdentifier = entry.photoIdentifier, !photoIdentifier.isEmpty {

                    if entry.isResized {
                        // 缩放模式：在左侧显示完整照片，右侧显示模糊背景
                        HStack(spacing: 0) {
                            // 左侧：完整照片显示区域
                            Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
//                                Image(uiImage: photo)
//                                    .resizable()
//                                    .aspectRatio(contentMode: .fit)
//                                    .frame(width: geometry.size.width * 0.6, height: geometry.size.width * 0.6 * photo.size.height / photo.size.width)
//                                    .clipped()
//                                    .overlay(
//                                        RoundedRectangle(cornerRadius: 10)
//                                            .strokeBorder(Color.white, lineWidth: 4) // 给图片添加白色边框
//                                            .padding(0) // 确保边框紧贴图片内容
//                                    )
                                
                                Image(uiImage: photo)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: geometry.size.width * 0.6, height: geometry.size.width * 0.6 * photo.size.height / photo.size.width)
                                    .clipped()
                                    .padding(2)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 10) // 或者使用 Rectangle()
                                            .stroke(Color.white, lineWidth: 5) // 设置边框颜色和宽度
                                    )
                            }

//                            // 右侧：模糊背景
//                            Image(uiImage: photo)
//                                .resizable()
//                                .aspectRatio(contentMode: .fill)
//                                .frame(width: geometry.size.width * 0.4, height: geometry.size.height)
//                                .clipped()
//                                .blur(radius: 10)
//                                .opacity(0.6)
                        }
                    } else {
                        // 正常模式：平铺显示
                        Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                            Image(uiImage: photo)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                                .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                        }
                    }

                } else if let photo = entry.photo {
                    // 有照片但没有标识符，只显示图片不可点击
                    if entry.isResized {
                        // 缩放模式
                        HStack(spacing: 0) {
                            Image(uiImage: photo)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: geometry.size.width * 0.6, height: geometry.size.height)
                                .clipped()

                            Image(uiImage: photo)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width * 0.4, height: geometry.size.height)
                                .clipped()
                                .blur(radius: 10)
                                .opacity(0.6)
                        }
                    } else {
                        // 正常模式
                        Image(uiImage: photo)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                            .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                    }
                } else {
                    // 默认背景
                    Link(destination: URL(string: "photoclear://openPhoto")!) {
                        Text("所有照片都已操作过，请点击打开APP更新缓存")
                            .font(.system(size: 16))
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
                
                // 时间显示区域 - 根据缩放状态调整位置
                VStack {
                    Spacer()
                    HStack {
                        if let photoMetadata = entry.photoMetadata,
                           let creationDate = photoMetadata.creationDate {
                            Text(formatPhotoDate(creationDate))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 10)
                                .padding(.vertical, 5)
                                .background(Color.black.opacity(0.2))
                                .cornerRadius(8)
                                .shadow(radius: 3)
                        }
                        Spacer()
                    }
                }
                .frame(width: geometry.size.width, height: geometry.size.height * 1)
                .offset(x: entry.isResized ? 0 : -geometry.size.width * offsetSize,
                       y: entry.isResized ? 0 : -geometry.size.height * offsetSize)
                
                // 按钮区域 - 根据缩放状态调整位置
                VStack {
                    HStack {
//                        if entry.isResized {
//                            // 缩放模式下，按钮显示在右侧区域的中央
//                            Spacer()
//                            Spacer()
//                            Spacer()
//                        } else {
//                            // 正常模式下，按钮显示在右上角
//                            
//                        }
                        Spacer()
                        VStack(spacing: 12) {

                            if let photoIdentifier = entry.photoIdentifier {

                                if entry.isResized {
                                    Button(intent: UnResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                        Image(systemName: "arrow.up.left.and.arrow.down.right")
                                            .font(.system(size: 16))
                                            .foregroundColor(.white)
                                            .frame(width: 32, height: 32) // 设置正方形尺寸
                                            .background(Color.black.opacity(0.2)) // 背景色
                                            .clipShape(Rectangle()) // 方形背景
                                            .cornerRadius(4) // 可选：给按钮加上圆角
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    
                                } else {
                                    Button(intent: ResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                        Image(systemName: "arrow.up.right.and.arrow.down.left")
                                            .font(.system(size: 16))
                                            .foregroundColor(.white)
                                            .frame(width: 32, height: 32) // 设置正方形尺寸
                                            .background(Color.black.opacity(0.2)) // 背景色
                                            .clipShape(Rectangle()) // 方形背景
                                            .cornerRadius(4) // 可选：给按钮加上圆角
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                                

                                Spacer()

                                Button(intent: ArchivePhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "archivebox")
                                        .font(.system(size: 16))
                                        .foregroundColor(.white)
                                        .frame(width: 32, height: 32) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(4) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())


                                // 切换到下一张照片按钮
                                Button(intent: NextPhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 16))
//                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: 32, height: 32) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(4) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())

                                // 标记删除按钮
                                Button(intent: MarkForDeletionIntent(photoIdentifier: photoIdentifier)) {

                                    Image(systemName: "trash")
                                        .font(.system(size: 14))
                                        .bold()
                                        .foregroundColor(.white)
                                        .frame(width: 32, height: 32) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(4) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())

                            }
                        }

//                        if entry.isResized {
//                            // 缩放模式下，在按钮右侧留一些空间
//                            Spacer()
//                        }
                    }

//                    if entry.isResized {
//                        // 缩放模式下，按钮垂直居中
//                        Spacer()
//                    }
                }

                .frame(width: geometry.size.width, height: geometry.size.height * 1)
                .offset(x: entry.isResized ? 0 : -geometry.size.width * offsetSize,
                       y: entry.isResized ? 0 : -geometry.size.height * offsetSize)
            }
        }
    }
}
